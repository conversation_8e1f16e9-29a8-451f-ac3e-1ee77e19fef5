  MainActivity com.example.examhots_app  FlutterActivity io.flutter.embedding.android  Activity android.app  Activity android.app.Activity  Date android.app.Activity  	Exception android.app.Activity  File android.app.Activity  FileProvider android.app.Activity  
FlutterEngine android.app.Activity  IOException android.app.Activity  Int android.app.Activity  Intent android.app.Activity  Locale android.app.Activity  
MediaStore android.app.Activity  
MethodChannel android.app.Activity  	RESULT_OK android.app.Activity  SimpleDateFormat android.app.Activity  String android.app.Activity  Throws android.app.Activity  Uri android.app.Activity  also android.app.Activity  android android.app.Activity  apply android.app.Activity  arrayOf android.app.Activity  configureFlutterEngine android.app.Activity  createImageFile android.app.Activity  currentPhotoPath android.app.Activity  getExternalFilesDir android.app.Activity  getPathFromUri android.app.Activity  onActivityResult android.app.Activity  
openCamera android.app.Activity  openFilePicker android.app.Activity  openGallery android.app.Activity  startActivityForResult android.app.Activity  use android.app.Activity  
ComponentName android.content  ContentResolver android.content  Intent android.content  equals android.content.ComponentName  query android.content.ContentResolver  Activity android.content.Context  Date android.content.Context  	Exception android.content.Context  File android.content.Context  FileProvider android.content.Context  
FlutterEngine android.content.Context  IOException android.content.Context  Int android.content.Context  Intent android.content.Context  Locale android.content.Context  
MediaStore android.content.Context  
MethodChannel android.content.Context  SimpleDateFormat android.content.Context  String android.content.Context  Throws android.content.Context  Uri android.content.Context  also android.content.Context  android android.content.Context  apply android.content.Context  arrayOf android.content.Context  configureFlutterEngine android.content.Context  createImageFile android.content.Context  currentPhotoPath android.content.Context  getExternalFilesDir android.content.Context  getPathFromUri android.content.Context  onActivityResult android.content.Context  
openCamera android.content.Context  openFilePicker android.content.Context  openGallery android.content.Context  startActivityForResult android.content.Context  use android.content.Context  Activity android.content.ContextWrapper  Date android.content.ContextWrapper  	Exception android.content.ContextWrapper  File android.content.ContextWrapper  FileProvider android.content.ContextWrapper  
FlutterEngine android.content.ContextWrapper  IOException android.content.ContextWrapper  Int android.content.ContextWrapper  Intent android.content.ContextWrapper  Locale android.content.ContextWrapper  
MediaStore android.content.ContextWrapper  
MethodChannel android.content.ContextWrapper  SimpleDateFormat android.content.ContextWrapper  String android.content.ContextWrapper  Throws android.content.ContextWrapper  Uri android.content.ContextWrapper  also android.content.ContextWrapper  android android.content.ContextWrapper  apply android.content.ContextWrapper  arrayOf android.content.ContextWrapper  configureFlutterEngine android.content.ContextWrapper  createImageFile android.content.ContextWrapper  currentPhotoPath android.content.ContextWrapper  getExternalFilesDir android.content.ContextWrapper  getPathFromUri android.content.ContextWrapper  onActivityResult android.content.ContextWrapper  
openCamera android.content.ContextWrapper  openFilePicker android.content.ContextWrapper  openGallery android.content.ContextWrapper  startActivityForResult android.content.ContextWrapper  use android.content.ContextWrapper  ACTION_GET_CONTENT android.content.Intent  ACTION_PICK android.content.Intent  CATEGORY_OPENABLE android.content.Intent  EXTRA_MIME_TYPES android.content.Intent  Intent android.content.Intent  addCategory android.content.Intent  apply android.content.Intent  arrayOf android.content.Intent  
createChooser android.content.Intent  data android.content.Intent  equals android.content.Intent  getAPPLY android.content.Intent  
getARRAYOf android.content.Intent  getApply android.content.Intent  
getArrayOf android.content.Intent  getDATA android.content.Intent  getData android.content.Intent  getTYPE android.content.Intent  getType android.content.Intent  putExtra android.content.Intent  resolveActivity android.content.Intent  setData android.content.Intent  setType android.content.Intent  type android.content.Intent  PackageManager android.content.pm  Cursor android.database  getColumnIndex android.database.Cursor  	getString android.database.Cursor  getUSE android.database.Cursor  getUse android.database.Cursor  moveToFirst android.database.Cursor  use android.database.Cursor  Uri android.net  equals android.net.Uri  getPATH android.net.Uri  getPath android.net.Uri  path android.net.Uri  setPath android.net.Uri  Environment 
android.os  DIRECTORY_PICTURES android.os.Environment  
MediaStore android.provider  ACTION_IMAGE_CAPTURE android.provider.MediaStore  EXTRA_OUTPUT android.provider.MediaStore  Images android.provider.MediaStore  Media "android.provider.MediaStore.Images  DATA (android.provider.MediaStore.Images.Media  EXTERNAL_CONTENT_URI (android.provider.MediaStore.Images.Media  Activity  android.view.ContextThemeWrapper  Date  android.view.ContextThemeWrapper  	Exception  android.view.ContextThemeWrapper  File  android.view.ContextThemeWrapper  FileProvider  android.view.ContextThemeWrapper  
FlutterEngine  android.view.ContextThemeWrapper  IOException  android.view.ContextThemeWrapper  Int  android.view.ContextThemeWrapper  Intent  android.view.ContextThemeWrapper  Locale  android.view.ContextThemeWrapper  
MediaStore  android.view.ContextThemeWrapper  
MethodChannel  android.view.ContextThemeWrapper  SimpleDateFormat  android.view.ContextThemeWrapper  String  android.view.ContextThemeWrapper  Throws  android.view.ContextThemeWrapper  Uri  android.view.ContextThemeWrapper  also  android.view.ContextThemeWrapper  android  android.view.ContextThemeWrapper  apply  android.view.ContextThemeWrapper  arrayOf  android.view.ContextThemeWrapper  configureFlutterEngine  android.view.ContextThemeWrapper  createImageFile  android.view.ContextThemeWrapper  currentPhotoPath  android.view.ContextThemeWrapper  getExternalFilesDir  android.view.ContextThemeWrapper  getPathFromUri  android.view.ContextThemeWrapper  onActivityResult  android.view.ContextThemeWrapper  
openCamera  android.view.ContextThemeWrapper  openFilePicker  android.view.ContextThemeWrapper  openGallery  android.view.ContextThemeWrapper  startActivityForResult  android.view.ContextThemeWrapper  use  android.view.ContextThemeWrapper  FileProvider androidx.core.content  
getUriForFile "androidx.core.content.FileProvider  Activity com.example.examhots_app  Date com.example.examhots_app  	Exception com.example.examhots_app  File com.example.examhots_app  FileProvider com.example.examhots_app  IOException com.example.examhots_app  Int com.example.examhots_app  Intent com.example.examhots_app  Locale com.example.examhots_app  
MediaStore com.example.examhots_app  
MethodChannel com.example.examhots_app  SimpleDateFormat com.example.examhots_app  String com.example.examhots_app  Throws com.example.examhots_app  also com.example.examhots_app  android com.example.examhots_app  apply com.example.examhots_app  arrayOf com.example.examhots_app  currentPhotoPath com.example.examhots_app  use com.example.examhots_app  Activity %com.example.examhots_app.MainActivity  CAMERA_REQUEST %com.example.examhots_app.MainActivity  CHANNEL %com.example.examhots_app.MainActivity  Date %com.example.examhots_app.MainActivity  	Exception %com.example.examhots_app.MainActivity  FILE_PICKER_REQUEST %com.example.examhots_app.MainActivity  File %com.example.examhots_app.MainActivity  FileProvider %com.example.examhots_app.MainActivity  
FlutterEngine %com.example.examhots_app.MainActivity  GALLERY_REQUEST %com.example.examhots_app.MainActivity  IOException %com.example.examhots_app.MainActivity  Int %com.example.examhots_app.MainActivity  Intent %com.example.examhots_app.MainActivity  Locale %com.example.examhots_app.MainActivity  
MediaStore %com.example.examhots_app.MainActivity  
MethodChannel %com.example.examhots_app.MainActivity  SimpleDateFormat %com.example.examhots_app.MainActivity  String %com.example.examhots_app.MainActivity  Throws %com.example.examhots_app.MainActivity  Uri %com.example.examhots_app.MainActivity  also %com.example.examhots_app.MainActivity  android %com.example.examhots_app.MainActivity  apply %com.example.examhots_app.MainActivity  arrayOf %com.example.examhots_app.MainActivity  contentResolver %com.example.examhots_app.MainActivity  createImageFile %com.example.examhots_app.MainActivity  currentPhotoPath %com.example.examhots_app.MainActivity  getALSO %com.example.examhots_app.MainActivity  
getANDROID %com.example.examhots_app.MainActivity  getAPPLY %com.example.examhots_app.MainActivity  
getARRAYOf %com.example.examhots_app.MainActivity  getAlso %com.example.examhots_app.MainActivity  
getAndroid %com.example.examhots_app.MainActivity  getApply %com.example.examhots_app.MainActivity  
getArrayOf %com.example.examhots_app.MainActivity  getCONTENTResolver %com.example.examhots_app.MainActivity  getContentResolver %com.example.examhots_app.MainActivity  getExternalFilesDir %com.example.examhots_app.MainActivity  getPACKAGEManager %com.example.examhots_app.MainActivity  getPackageManager %com.example.examhots_app.MainActivity  getPathFromUri %com.example.examhots_app.MainActivity  getUSE %com.example.examhots_app.MainActivity  getUse %com.example.examhots_app.MainActivity  
openCamera %com.example.examhots_app.MainActivity  openFilePicker %com.example.examhots_app.MainActivity  openGallery %com.example.examhots_app.MainActivity  packageManager %com.example.examhots_app.MainActivity  
pendingResult %com.example.examhots_app.MainActivity  setContentResolver %com.example.examhots_app.MainActivity  setPackageManager %com.example.examhots_app.MainActivity  startActivityForResult %com.example.examhots_app.MainActivity  use %com.example.examhots_app.MainActivity  Activity ,io.flutter.embedding.android.FlutterActivity  Date ,io.flutter.embedding.android.FlutterActivity  	Exception ,io.flutter.embedding.android.FlutterActivity  File ,io.flutter.embedding.android.FlutterActivity  FileProvider ,io.flutter.embedding.android.FlutterActivity  
FlutterEngine ,io.flutter.embedding.android.FlutterActivity  IOException ,io.flutter.embedding.android.FlutterActivity  Int ,io.flutter.embedding.android.FlutterActivity  Intent ,io.flutter.embedding.android.FlutterActivity  Locale ,io.flutter.embedding.android.FlutterActivity  
MediaStore ,io.flutter.embedding.android.FlutterActivity  
MethodChannel ,io.flutter.embedding.android.FlutterActivity  SimpleDateFormat ,io.flutter.embedding.android.FlutterActivity  String ,io.flutter.embedding.android.FlutterActivity  Throws ,io.flutter.embedding.android.FlutterActivity  Uri ,io.flutter.embedding.android.FlutterActivity  also ,io.flutter.embedding.android.FlutterActivity  android ,io.flutter.embedding.android.FlutterActivity  apply ,io.flutter.embedding.android.FlutterActivity  arrayOf ,io.flutter.embedding.android.FlutterActivity  configureFlutterEngine ,io.flutter.embedding.android.FlutterActivity  createImageFile ,io.flutter.embedding.android.FlutterActivity  currentPhotoPath ,io.flutter.embedding.android.FlutterActivity  getExternalFilesDir ,io.flutter.embedding.android.FlutterActivity  getPathFromUri ,io.flutter.embedding.android.FlutterActivity  onActivityResult ,io.flutter.embedding.android.FlutterActivity  
openCamera ,io.flutter.embedding.android.FlutterActivity  openFilePicker ,io.flutter.embedding.android.FlutterActivity  openGallery ,io.flutter.embedding.android.FlutterActivity  startActivityForResult ,io.flutter.embedding.android.FlutterActivity  use ,io.flutter.embedding.android.FlutterActivity  
FlutterEngine io.flutter.embedding.engine  dartExecutor )io.flutter.embedding.engine.FlutterEngine  getDARTExecutor )io.flutter.embedding.engine.FlutterEngine  getDartExecutor )io.flutter.embedding.engine.FlutterEngine  setDartExecutor )io.flutter.embedding.engine.FlutterEngine  binaryMessenger -io.flutter.embedding.engine.dart.DartExecutor  getBINARYMessenger -io.flutter.embedding.engine.dart.DartExecutor  getBinaryMessenger -io.flutter.embedding.engine.dart.DartExecutor  setBinaryMessenger -io.flutter.embedding.engine.dart.DartExecutor  BinaryMessenger io.flutter.plugin.common  
MethodCall io.flutter.plugin.common  
MethodChannel io.flutter.plugin.common  method #io.flutter.plugin.common.MethodCall  Result &io.flutter.plugin.common.MethodChannel  setMethodCallHandler &io.flutter.plugin.common.MethodChannel  <SAM-CONSTRUCTOR> 8io.flutter.plugin.common.MethodChannel.MethodCallHandler  error -io.flutter.plugin.common.MethodChannel.Result  notImplemented -io.flutter.plugin.common.MethodChannel.Result  success -io.flutter.plugin.common.MethodChannel.Result  File java.io  IOException java.io  absolutePath java.io.File  also java.io.File  apply java.io.File  createTempFile java.io.File  currentPhotoPath java.io.File  getABSOLUTEPath java.io.File  getALSO java.io.File  getAPPLY java.io.File  getAbsolutePath java.io.File  getAlso java.io.File  getApply java.io.File  getCURRENTPhotoPath java.io.File  getCurrentPhotoPath java.io.File  setAbsolutePath java.io.File  Activity 	java.lang  Date 	java.lang  	Exception 	java.lang  File 	java.lang  FileProvider 	java.lang  IOException 	java.lang  Intent 	java.lang  Locale 	java.lang  
MediaStore 	java.lang  
MethodChannel 	java.lang  SimpleDateFormat 	java.lang  also 	java.lang  android 	java.lang  apply 	java.lang  arrayOf 	java.lang  currentPhotoPath 	java.lang  use 	java.lang  message java.lang.Exception  SimpleDateFormat 	java.text  format java.text.DateFormat  format java.text.Format  format java.text.SimpleDateFormat  Activity 	java.util  Date 	java.util  	Exception 	java.util  File 	java.util  FileProvider 	java.util  IOException 	java.util  Intent 	java.util  Locale 	java.util  
MediaStore 	java.util  
MethodChannel 	java.util  SimpleDateFormat 	java.util  Throws 	java.util  also 	java.util  android 	java.util  apply 	java.util  arrayOf 	java.util  currentPhotoPath 	java.util  use 	java.util  
getDefault java.util.Locale  Activity kotlin  Any kotlin  Array kotlin  Boolean kotlin  Date kotlin  	Exception kotlin  File kotlin  FileProvider kotlin  	Function1 kotlin  	Function2 kotlin  IOException kotlin  Int kotlin  Intent kotlin  Locale kotlin  
MediaStore kotlin  
MethodChannel kotlin  Nothing kotlin  SimpleDateFormat kotlin  String kotlin  Throws kotlin  Unit kotlin  also kotlin  android kotlin  apply kotlin  arrayOf kotlin  currentPhotoPath kotlin  use kotlin  Activity kotlin.annotation  Date kotlin.annotation  	Exception kotlin.annotation  File kotlin.annotation  FileProvider kotlin.annotation  IOException kotlin.annotation  Intent kotlin.annotation  Locale kotlin.annotation  
MediaStore kotlin.annotation  
MethodChannel kotlin.annotation  SimpleDateFormat kotlin.annotation  Throws kotlin.annotation  also kotlin.annotation  android kotlin.annotation  apply kotlin.annotation  arrayOf kotlin.annotation  currentPhotoPath kotlin.annotation  use kotlin.annotation  Activity kotlin.collections  Date kotlin.collections  	Exception kotlin.collections  File kotlin.collections  FileProvider kotlin.collections  IOException kotlin.collections  Intent kotlin.collections  Locale kotlin.collections  
MediaStore kotlin.collections  
MethodChannel kotlin.collections  SimpleDateFormat kotlin.collections  Throws kotlin.collections  also kotlin.collections  android kotlin.collections  apply kotlin.collections  arrayOf kotlin.collections  currentPhotoPath kotlin.collections  use kotlin.collections  Activity kotlin.comparisons  Date kotlin.comparisons  	Exception kotlin.comparisons  File kotlin.comparisons  FileProvider kotlin.comparisons  IOException kotlin.comparisons  Intent kotlin.comparisons  Locale kotlin.comparisons  
MediaStore kotlin.comparisons  
MethodChannel kotlin.comparisons  SimpleDateFormat kotlin.comparisons  Throws kotlin.comparisons  also kotlin.comparisons  android kotlin.comparisons  apply kotlin.comparisons  arrayOf kotlin.comparisons  currentPhotoPath kotlin.comparisons  use kotlin.comparisons  Activity 	kotlin.io  Date 	kotlin.io  	Exception 	kotlin.io  File 	kotlin.io  FileProvider 	kotlin.io  IOException 	kotlin.io  Intent 	kotlin.io  Locale 	kotlin.io  
MediaStore 	kotlin.io  
MethodChannel 	kotlin.io  SimpleDateFormat 	kotlin.io  Throws 	kotlin.io  also 	kotlin.io  android 	kotlin.io  apply 	kotlin.io  arrayOf 	kotlin.io  currentPhotoPath 	kotlin.io  use 	kotlin.io  Activity 
kotlin.jvm  Date 
kotlin.jvm  	Exception 
kotlin.jvm  File 
kotlin.jvm  FileProvider 
kotlin.jvm  IOException 
kotlin.jvm  Intent 
kotlin.jvm  Locale 
kotlin.jvm  
MediaStore 
kotlin.jvm  
MethodChannel 
kotlin.jvm  SimpleDateFormat 
kotlin.jvm  Throws 
kotlin.jvm  also 
kotlin.jvm  android 
kotlin.jvm  apply 
kotlin.jvm  arrayOf 
kotlin.jvm  currentPhotoPath 
kotlin.jvm  use 
kotlin.jvm  Activity 
kotlin.ranges  Date 
kotlin.ranges  	Exception 
kotlin.ranges  File 
kotlin.ranges  FileProvider 
kotlin.ranges  IOException 
kotlin.ranges  Intent 
kotlin.ranges  Locale 
kotlin.ranges  
MediaStore 
kotlin.ranges  
MethodChannel 
kotlin.ranges  SimpleDateFormat 
kotlin.ranges  Throws 
kotlin.ranges  also 
kotlin.ranges  android 
kotlin.ranges  apply 
kotlin.ranges  arrayOf 
kotlin.ranges  currentPhotoPath 
kotlin.ranges  use 
kotlin.ranges  KClass kotlin.reflect  Activity kotlin.sequences  Date kotlin.sequences  	Exception kotlin.sequences  File kotlin.sequences  FileProvider kotlin.sequences  IOException kotlin.sequences  Intent kotlin.sequences  Locale kotlin.sequences  
MediaStore kotlin.sequences  
MethodChannel kotlin.sequences  SimpleDateFormat kotlin.sequences  Throws kotlin.sequences  also kotlin.sequences  android kotlin.sequences  apply kotlin.sequences  arrayOf kotlin.sequences  currentPhotoPath kotlin.sequences  use kotlin.sequences  Activity kotlin.text  Date kotlin.text  	Exception kotlin.text  File kotlin.text  FileProvider kotlin.text  IOException kotlin.text  Intent kotlin.text  Locale kotlin.text  
MediaStore kotlin.text  
MethodChannel kotlin.text  SimpleDateFormat kotlin.text  Throws kotlin.text  also kotlin.text  android kotlin.text  apply kotlin.text  arrayOf kotlin.text  currentPhotoPath kotlin.text  use kotlin.text  Log android.app.Activity  Log android.content.Context  Log android.content.ContextWrapper  toString android.net.Uri  Log android.util  d android.util.Log  e android.util.Log  Log  android.view.ContextThemeWrapper  Log com.example.examhots_app  Log %com.example.examhots_app.MainActivity  Log ,io.flutter.embedding.android.FlutterActivity  Log 	java.lang  Log kotlin  Log kotlin.annotation  Log kotlin.collections  Log kotlin.comparisons  Log 	kotlin.io  Log 
kotlin.jvm  Log 
kotlin.ranges  Log kotlin.sequences  Log kotlin.text  Suppress android.app.Activity  Suppress android.content.Context  Suppress android.content.ContextWrapper  Suppress  android.view.ContextThemeWrapper  Suppress com.example.examhots_app  Suppress %com.example.examhots_app.MainActivity  Suppress ,io.flutter.embedding.android.FlutterActivity  Suppress kotlin                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             