1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.examhots_app"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:10:5-67
15-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:10:22-64
16    <!-- Permissions for camera and file access -->
17    <uses-feature
17-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:3:5-5:36
18        android:name="android.hardware.camera"
18-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:4:9-47
19        android:required="false" />
19-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:5:9-33
20
21    <uses-permission android:name="android.permission.CAMERA" />
21-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:7:5-65
21-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:7:22-62
22    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
22-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:8:5-80
22-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:8:22-77
23    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" /> <!-- Permissions for Android 13+ (API 33+) -->
23-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:9:5-81
23-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:9:22-78
24    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
24-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:13:5-76
24-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:13:22-73
25    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
25-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:14:5-75
25-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:14:22-72
26    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" /> <!-- Permission for accessing all files (Android 11+) -->
26-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:15:5-75
26-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:15:22-72
27    <uses-permission
27-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:18:5-19:38
28        android:name="android.permission.MANAGE_EXTERNAL_STORAGE"
28-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:18:22-79
29        android:maxSdkVersion="32" />
29-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:19:9-35
30    <!--
31 Required to query activities that can process text, see:
32         https://developer.android.com/training/package-visibility and
33         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
34
35         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
36    -->
37    <queries>
37-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:69:5-74:15
38        <intent>
38-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:70:9-73:18
39            <action android:name="android.intent.action.PROCESS_TEXT" />
39-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:71:13-73
39-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:71:21-70
40
41            <data android:mimeType="text/plain" />
41-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:72:13-51
41-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:72:19-48
42        </intent>
43        <intent>
43-->[:file_picker] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:18
44            <action android:name="android.intent.action.GET_CONTENT" />
44-->[:file_picker] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-72
44-->[:file_picker] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:21-69
45
46            <data android:mimeType="*/*" />
46-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:72:13-51
46-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:72:19-48
47        </intent>
48    </queries>
49
50    <permission
50-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\43a41165a90de7b0bfc02b6ed57eebd0\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
51        android:name="com.example.examhots_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
51-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\43a41165a90de7b0bfc02b6ed57eebd0\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
52        android:protectionLevel="signature" />
52-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\43a41165a90de7b0bfc02b6ed57eebd0\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
53
54    <uses-permission android:name="com.example.examhots_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
54-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\43a41165a90de7b0bfc02b6ed57eebd0\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
54-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\43a41165a90de7b0bfc02b6ed57eebd0\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
55
56    <application
57        android:name="android.app.Application"
58        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
58-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\43a41165a90de7b0bfc02b6ed57eebd0\transformed\core-1.15.0\AndroidManifest.xml:28:18-86
59        android:debuggable="true"
60        android:extractNativeLibs="true"
61        android:icon="@mipmap/launcher_icon"
62        android:label="Hots UM" >
63        <activity
64            android:name="com.example.examhots_app.MainActivity"
65            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
66            android:exported="true"
67            android:hardwareAccelerated="true"
68            android:launchMode="singleTop"
69            android:taskAffinity=""
70            android:theme="@style/LaunchTheme"
71            android:windowSoftInputMode="adjustResize" >
72
73            <!--
74                 Specifies an Android theme to apply to this Activity as soon as
75                 the Android process has started. This theme is visible to the user
76                 while the Flutter UI initializes. After that, this theme continues
77                 to determine the Window background behind the Flutter UI.
78            -->
79            <meta-data
80                android:name="io.flutter.embedding.android.NormalTheme"
81                android:resource="@style/NormalTheme" />
82
83            <intent-filter>
84                <action android:name="android.intent.action.MAIN" />
85
86                <category android:name="android.intent.category.LAUNCHER" />
87            </intent-filter>
88        </activity>
89
90        <!-- FileProvider for camera functionality -->
91        <provider
92            android:name="androidx.core.content.FileProvider"
93            android:authorities="com.example.examhots_app.fileprovider"
94            android:exported="false"
95            android:grantUriPermissions="true" >
96            <meta-data
96-->[:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-16:75
97                android:name="android.support.FILE_PROVIDER_PATHS"
97-->[:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-67
98                android:resource="@xml/file_paths" />
98-->[:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:17-72
99        </provider>
100
101        <!--
102             Don't delete the meta-data below.
103             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
104        -->
105        <meta-data
106            android:name="flutterEmbedding"
107            android:value="2" />
108
109        <provider
109-->[:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-17:20
110            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
110-->[:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-82
111            android:authorities="com.example.examhots_app.flutter.image_provider"
111-->[:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-74
112            android:exported="false"
112-->[:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-37
113            android:grantUriPermissions="true" >
113-->[:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-47
114            <meta-data
114-->[:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-16:75
115                android:name="android.support.FILE_PROVIDER_PATHS"
115-->[:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-67
116                android:resource="@xml/flutter_image_picker_file_paths" />
116-->[:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:17-72
117        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
118        <service
118-->[:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-31:19
119            android:name="com.google.android.gms.metadata.ModuleDependencies"
119-->[:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-78
120            android:enabled="false"
120-->[:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-36
121            android:exported="false" >
121-->[:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-37
122            <intent-filter>
122-->[:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-26:29
123                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
123-->[:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-94
123-->[:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-91
124            </intent-filter>
125
126            <meta-data
126-->[:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-30:36
127                android:name="photopicker_activity:0:required"
127-->[:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-63
128                android:value="" />
128-->[:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-33
129        </service>
130
131        <activity
131-->[:url_launcher_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-11:74
132            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
132-->[:url_launcher_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-74
133            android:exported="false"
133-->[:url_launcher_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-37
134            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
134-->[:url_launcher_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-71
135
136        <provider
136-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\43ebce9c1ba25a34b2589ddcb28a686a\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:24:9-32:20
137            android:name="androidx.startup.InitializationProvider"
137-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\43ebce9c1ba25a34b2589ddcb28a686a\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:25:13-67
138            android:authorities="com.example.examhots_app.androidx-startup"
138-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\43ebce9c1ba25a34b2589ddcb28a686a\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:26:13-68
139            android:exported="false" >
139-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\43ebce9c1ba25a34b2589ddcb28a686a\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:27:13-37
140            <meta-data
140-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\43ebce9c1ba25a34b2589ddcb28a686a\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:29:13-31:52
141                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
141-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\43ebce9c1ba25a34b2589ddcb28a686a\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:30:17-78
142                android:value="androidx.startup" />
142-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\43ebce9c1ba25a34b2589ddcb28a686a\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:31:17-49
143            <meta-data
143-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
144                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
144-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
145                android:value="androidx.startup" />
145-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
146        </provider>
147
148        <uses-library
148-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\970764a81dd610c772147726baa7dca5\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
149            android:name="androidx.window.extensions"
149-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\970764a81dd610c772147726baa7dca5\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
150            android:required="false" />
150-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\970764a81dd610c772147726baa7dca5\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
151        <uses-library
151-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\970764a81dd610c772147726baa7dca5\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
152            android:name="androidx.window.sidecar"
152-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\970764a81dd610c772147726baa7dca5\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
153            android:required="false" />
153-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\970764a81dd610c772147726baa7dca5\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
154
155        <receiver
155-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
156            android:name="androidx.profileinstaller.ProfileInstallReceiver"
156-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
157            android:directBootAware="false"
157-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
158            android:enabled="true"
158-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
159            android:exported="true"
159-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
160            android:permission="android.permission.DUMP" >
160-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
161            <intent-filter>
161-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
162                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
162-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
162-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
163            </intent-filter>
164            <intent-filter>
164-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
165                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
165-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
165-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
166            </intent-filter>
167            <intent-filter>
167-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
168                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
168-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
168-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
169            </intent-filter>
170            <intent-filter>
170-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
171                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
171-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
171-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
172            </intent-filter>
173        </receiver>
174    </application>
175
176</manifest>
