{"buildFiles": ["C:\\Users\\<USER>\\AppData\\Local\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\xampp-8.2-new\\htdocs\\examhots\\examhots_app\\build\\.cxx\\Debug\\2k6x1h2u\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\xampp-8.2-new\\htdocs\\examhots\\examhots_app\\build\\.cxx\\Debug\\2k6x1h2u\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}