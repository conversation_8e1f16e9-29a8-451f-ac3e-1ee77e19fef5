package com.example.examhots_app

import android.app.Activity
import android.content.Intent
import android.net.Uri
import android.provider.MediaStore
import android.util.Log
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel

class MainActivity : FlutterActivity() {
    private val CHANNEL = "native_file_picker"
    private val FILE_PICKER_REQUEST = 1001
    private val CAMERA_REQUEST = 1002
    private val GALLERY_REQUEST = 1003

    private var pendingResult: MethodChannel.Result? = null

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)

        try {
            MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL).setMethodCallHandler { call, result ->
                Log.d("NativeFilePicker", "Method called: ${call.method}")
                pendingResult = result

                try {
                    when (call.method) {
                        "openFilePicker" -> {
                            Log.d("NativeFilePicker", "Opening file picker")
                            openFilePicker()
                        }
                        "openCamera" -> {
                            Log.d("NativeFilePicker", "Opening camera")
                            openCamera()
                        }
                        "openGallery" -> {
                            Log.d("NativeFilePicker", "Opening gallery")
                            openGallery()
                        }
                        else -> {
                            Log.d("NativeFilePicker", "Method not implemented: ${call.method}")
                            result.notImplemented()
                            pendingResult = null
                        }
                    }
                } catch (e: Exception) {
                    Log.e("NativeFilePicker", "Error in method handler: ${e.message}", e)
                    result.error("METHOD_ERROR", "Error: ${e.message}", null)
                    pendingResult = null
                }
            }
        } catch (e: Exception) {
            Log.e("NativeFilePicker", "Error configuring Flutter engine: ${e.message}", e)
        }
    }



    private fun openFilePicker() {
        try {
            Log.d("NativeFilePicker", "Creating simple file picker intent")

            val intent = Intent(Intent.ACTION_GET_CONTENT).apply {
                type = "*/*"
                addCategory(Intent.CATEGORY_OPENABLE)
            }

            Log.d("NativeFilePicker", "Starting file picker with startActivityForResult")
            @Suppress("DEPRECATION")
            startActivityForResult(Intent.createChooser(intent, "Pilih File"), FILE_PICKER_REQUEST)

        } catch (e: Exception) {
            Log.e("NativeFilePicker", "Error in openFilePicker: ${e.message}", e)
            pendingResult?.error("FILE_PICKER_ERROR", "Error opening file picker: ${e.message}", null)
            pendingResult = null
        }
    }

    private fun openCamera() {
        try {
            Log.d("NativeFilePicker", "Creating simple camera intent")

            val takePictureIntent = Intent(MediaStore.ACTION_IMAGE_CAPTURE)

            if (takePictureIntent.resolveActivity(packageManager) != null) {
                Log.d("NativeFilePicker", "Starting camera with startActivityForResult")
                @Suppress("DEPRECATION")
                startActivityForResult(takePictureIntent, CAMERA_REQUEST)
            } else {
                Log.e("NativeFilePicker", "Camera not available")
                pendingResult?.error("CAMERA_ERROR", "Camera not available", null)
                pendingResult = null
            }
        } catch (e: Exception) {
            Log.e("NativeFilePicker", "Error in openCamera: ${e.message}", e)
            pendingResult?.error("CAMERA_ERROR", "Error opening camera: ${e.message}", null)
            pendingResult = null
        }
    }

    private fun openGallery() {
        try {
            Log.d("NativeFilePicker", "Creating simple gallery intent")

            val intent = Intent(Intent.ACTION_PICK, MediaStore.Images.Media.EXTERNAL_CONTENT_URI)

            Log.d("NativeFilePicker", "Starting gallery with startActivityForResult")
            @Suppress("DEPRECATION")
            startActivityForResult(intent, GALLERY_REQUEST)

        } catch (e: Exception) {
            Log.e("NativeFilePicker", "Error in openGallery: ${e.message}", e)
            pendingResult?.error("GALLERY_ERROR", "Error opening gallery: ${e.message}", null)
            pendingResult = null
        }
    }

    @Suppress("DEPRECATION")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        Log.d("NativeFilePicker", "onActivityResult: requestCode=$requestCode, resultCode=$resultCode")

        try {
            when (requestCode) {
                FILE_PICKER_REQUEST -> {
                    Log.d("NativeFilePicker", "File picker result")
                    if (resultCode == Activity.RESULT_OK && data != null) {
                        val uri = data.data
                        Log.d("NativeFilePicker", "File selected: $uri")
                        pendingResult?.success(uri?.toString())
                    } else {
                        Log.d("NativeFilePicker", "File picker cancelled")
                        pendingResult?.success(null)
                    }
                    pendingResult = null
                }
                CAMERA_REQUEST -> {
                    Log.d("NativeFilePicker", "Camera result")
                    if (resultCode == Activity.RESULT_OK) {
                        Log.d("NativeFilePicker", "Photo taken successfully")
                        pendingResult?.success("camera_success")
                    } else {
                        Log.d("NativeFilePicker", "Camera cancelled")
                        pendingResult?.success(null)
                    }
                    pendingResult = null
                }
                GALLERY_REQUEST -> {
                    Log.d("NativeFilePicker", "Gallery result")
                    if (resultCode == Activity.RESULT_OK && data != null) {
                        val uri = data.data
                        Log.d("NativeFilePicker", "Image selected: $uri")
                        pendingResult?.success(uri?.toString())
                    } else {
                        Log.d("NativeFilePicker", "Gallery cancelled")
                        pendingResult?.success(null)
                    }
                    pendingResult = null
                }
            }
        } catch (e: Exception) {
            Log.e("NativeFilePicker", "Error in onActivityResult: ${e.message}", e)
            pendingResult?.error("RESULT_ERROR", "Error processing result: ${e.message}", null)
            pendingResult = null
        }
    }
}
