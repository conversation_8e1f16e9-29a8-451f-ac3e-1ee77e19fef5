import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';
import 'dart:io';

class FilePickerPage extends StatefulWidget {
  final int questionId;

  const FilePickerPage({Key? key, required this.questionId}) : super(key: key);

  @override
  State<FilePickerPage> createState() => _FilePickerPageState();
}

class _FilePickerPageState extends State<FilePickerPage> {
  File? _selectedFile;
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Pilih File'),
        backgroundColor: const Color(0xFF455A9D),
        foregroundColor: Colors.white,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              'Pilih file untuk melampirkan jawaban Anda:',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
            ),
            const SizedBox(height: 24),

            // File Picker Button
            ElevatedButton.icon(
              onPressed:
                  _isLoading ? null : () async => await _pickFileFromStorage(),
              icon:
                  _isLoading
                      ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                      : const Icon(Icons.folder_open),
              label: Text(
                _isLoading ? 'Memilih file...' : 'Pilih Gambar/Dokumen',
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF455A9D),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
            ),

            const SizedBox(height: 16),

            // Camera Button
            ElevatedButton.icon(
              onPressed:
                  _isLoading ? null : () async => await _pickImageFromCamera(),
              icon:
                  _isLoading
                      ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                      : const Icon(Icons.camera_alt),
              label: Text(_isLoading ? 'Membuka kamera...' : 'Ambil Foto'),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF31406F),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
            ),

            const SizedBox(height: 16),

            // Gallery Button
            ElevatedButton.icon(
              onPressed:
                  _isLoading ? null : () async => await _pickImageFromGallery(),
              icon:
                  _isLoading
                      ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                      : const Icon(Icons.photo_library),
              label: Text(
                _isLoading ? 'Membuka galeri...' : 'Pilih dari Galeri',
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF667085),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
            ),

            const SizedBox(height: 32),

            // Selected File Preview
            if (_selectedFile != null) ...[
              const Text(
                'File yang dipilih:',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
              ),
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey[300]!),
                ),
                child: Row(
                  children: [
                    Icon(
                      _getFileIcon(_selectedFile!.path),
                      color: const Color(0xFF455A9D),
                      size: 32,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            _getFileName(_selectedFile!.path),
                            style: const TextStyle(fontWeight: FontWeight.w500),
                          ),
                          Text(
                            _getFileSize(_selectedFile!),
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),
                    IconButton(
                      onPressed: () {
                        setState(() {
                          _selectedFile = null;
                        });
                      },
                      icon: const Icon(Icons.close, color: Colors.red),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 24),

              // Confirm Button
              ElevatedButton(
                onPressed: _confirmSelection,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: const Text(
                  'Gunakan File Ini',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                ),
              ),
            ],

            const Spacer(),

            // Info Text
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue[200]!),
              ),
              child: const Text(
                'Cara menggunakan:\n'
                '• Pilih Gambar/Dokumen: Buka galeri untuk memilih file\n'
                '• Ambil Foto: Gunakan kamera untuk mengambil foto\n'
                '• Pilih dari Galeri: Pilih gambar yang sudah ada',
                style: TextStyle(fontSize: 14, color: Color(0xFF455A9D)),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _pickFileFromStorage() async {
    print('DEBUG: _pickFileFromStorage started');

    setState(() {
      _isLoading = true;
    });

    try {
      print('DEBUG: Checking storage permission');

      // Request storage permission first
      bool hasPermission = await _requestStoragePermission();

      if (!hasPermission) {
        _showErrorMessage(
          'Izin akses penyimpanan diperlukan untuk memilih file',
        );
        print('DEBUG: Storage permission denied');
        return;
      }

      print('DEBUG: Storage permission granted, calling FilePicker');

      // Use file_picker package with proper await
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['jpg', 'jpeg', 'png', 'pdf', 'doc', 'docx'],
        allowMultiple: false,
      );

      print('DEBUG: After FilePicker.platform.pickFiles call');

      if (result != null && result.files.single.path != null) {
        final File selectedFile = File(result.files.single.path!);

        setState(() {
          _selectedFile = selectedFile;
        });

        _showSuccessMessage('File berhasil dipilih');
        print('DEBUG: File successfully selected: ${result.files.single.path}');
      } else {
        print('DEBUG: No file selected or cancelled');
      }
    } catch (e) {
      print('DEBUG: Error caught: $e');
      _showErrorMessage('Error memilih file: $e');
    } finally {
      print('DEBUG: Finally block executed');
      setState(() {
        _isLoading = false;
      });
    }

    print('DEBUG: _pickFileFromStorage completed');
  }

  // Request storage permission
  Future<bool> _requestStoragePermission() async {
    try {
      print('DEBUG: Requesting storage permission');

      // For Android 13+ (API 33+), we need different permissions

      if (await Permission.photos.isGranted ||
          await Permission.storage.isGranted) {
        print('DEBUG: Storage permission already granted');
        return true;
      }

      // Request permission
      Map<Permission, PermissionStatus> statuses =
          await [
            Permission.storage,
            Permission.photos,
            Permission.manageExternalStorage,
          ].request();

      // Check if any storage permission is granted
      bool isGranted =
          statuses[Permission.storage] == PermissionStatus.granted ||
          statuses[Permission.photos] == PermissionStatus.granted ||
          statuses[Permission.manageExternalStorage] ==
              PermissionStatus.granted;

      print('DEBUG: Storage permission result: $isGranted');

      if (!isGranted) {
        // Show dialog to open app settings
        _showPermissionDialog();
      }

      return isGranted;
    } catch (e) {
      print('DEBUG: Error requesting storage permission: $e');
      return false;
    }
  }

  // Show permission dialog
  void _showPermissionDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Izin Diperlukan'),
          content: const Text(
            'Aplikasi memerlukan izin akses penyimpanan untuk memilih file. '
            'Silakan berikan izin di pengaturan aplikasi.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Batal'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                openAppSettings();
              },
              child: const Text('Buka Pengaturan'),
            ),
          ],
        );
      },
    );
  }

  Future<void> _pickImageFromCamera() async {
    setState(() {
      _isLoading = true;
    });

    try {
      print('DEBUG: Checking camera permission');

      // Request camera permission first
      bool hasPermission = await _requestCameraPermission();

      if (!hasPermission) {
        _showErrorMessage('Izin akses kamera diperlukan untuk mengambil foto');
        print('DEBUG: Camera permission denied');
        return;
      }

      print('DEBUG: Camera permission granted, calling ImagePicker');

      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.camera,
        imageQuality: 80,
        maxWidth: 1920,
        maxHeight: 1080,
      );

      print('DEBUG: After ImagePicker camera call');

      if (image != null) {
        final File selectedFile = File(image.path);

        setState(() {
          _selectedFile = selectedFile;
        });

        _showSuccessMessage('Foto berhasil diambil');
        print('DEBUG: Photo successfully taken: ${image.path}');
      } else {
        print('DEBUG: No photo taken or cancelled');
      }
    } catch (e) {
      print('DEBUG: Camera error: $e');
      _showErrorMessage('Error mengambil foto: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  // Request camera permission
  Future<bool> _requestCameraPermission() async {
    try {
      print('DEBUG: Requesting camera permission');

      if (await Permission.camera.isGranted) {
        print('DEBUG: Camera permission already granted');
        return true;
      }

      PermissionStatus status = await Permission.camera.request();

      bool isGranted = status == PermissionStatus.granted;
      print('DEBUG: Camera permission result: $isGranted');

      if (!isGranted) {
        _showCameraPermissionDialog();
      }

      return isGranted;
    } catch (e) {
      print('DEBUG: Error requesting camera permission: $e');
      return false;
    }
  }

  // Show camera permission dialog
  void _showCameraPermissionDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Izin Kamera Diperlukan'),
          content: const Text(
            'Aplikasi memerlukan izin akses kamera untuk mengambil foto. '
            'Silakan berikan izin di pengaturan aplikasi.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Batal'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                openAppSettings();
              },
              child: const Text('Buka Pengaturan'),
            ),
          ],
        );
      },
    );
  }

  Future<void> _pickImageFromGallery() async {
    setState(() {
      _isLoading = true;
    });

    try {
      print('DEBUG: Checking gallery permission');

      // Request gallery permission first
      bool hasPermission = await _requestGalleryPermission();

      if (!hasPermission) {
        _showErrorMessage('Izin akses galeri diperlukan untuk memilih gambar');
        print('DEBUG: Gallery permission denied');
        return;
      }

      print('DEBUG: Gallery permission granted, calling ImagePicker');

      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 80,
        maxWidth: 1920,
        maxHeight: 1080,
      );

      print('DEBUG: After ImagePicker gallery call');

      if (image != null) {
        final File selectedFile = File(image.path);

        setState(() {
          _selectedFile = selectedFile;
        });

        _showSuccessMessage('Gambar berhasil dipilih');
        print('DEBUG: Image successfully selected: ${image.path}');
      } else {
        print('DEBUG: No image selected or cancelled');
      }
    } catch (e) {
      print('DEBUG: Gallery error: $e');
      _showErrorMessage('Error memilih gambar: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  // Request gallery permission
  Future<bool> _requestGalleryPermission() async {
    try {
      print('DEBUG: Requesting gallery permission');

      // Check if permission is already granted
      if (await Permission.photos.isGranted ||
          await Permission.storage.isGranted) {
        print('DEBUG: Gallery permission already granted');
        return true;
      }

      // Request permission
      Map<Permission, PermissionStatus> statuses =
          await [Permission.photos, Permission.storage].request();

      bool isGranted =
          statuses[Permission.photos] == PermissionStatus.granted ||
          statuses[Permission.storage] == PermissionStatus.granted;

      print('DEBUG: Gallery permission result: $isGranted');

      if (!isGranted) {
        _showGalleryPermissionDialog();
      }

      return isGranted;
    } catch (e) {
      print('DEBUG: Error requesting gallery permission: $e');
      return false;
    }
  }

  // Show gallery permission dialog
  void _showGalleryPermissionDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Izin Galeri Diperlukan'),
          content: const Text(
            'Aplikasi memerlukan izin akses galeri untuk memilih gambar. '
            'Silakan berikan izin di pengaturan aplikasi.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Batal'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                openAppSettings();
              },
              child: const Text('Buka Pengaturan'),
            ),
          ],
        );
      },
    );
  }

  void _confirmSelection() {
    if (_selectedFile != null) {
      Navigator.of(context).pop(_selectedFile);
    }
  }

  void _showSuccessMessage(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.green,
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }

  void _showErrorMessage(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  IconData _getFileIcon(String filePath) {
    final extension = filePath.split('.').last.toLowerCase();
    switch (extension) {
      case 'jpg':
      case 'jpeg':
      case 'png':
        return Icons.image;
      case 'pdf':
        return Icons.picture_as_pdf;
      case 'doc':
      case 'docx':
        return Icons.description;
      default:
        return Icons.insert_drive_file;
    }
  }

  String _getFileName(String filePath) {
    return filePath.split('/').last;
  }

  String _getFileSize(File file) {
    try {
      final bytes = file.lengthSync();
      if (bytes < 1024) {
        return '$bytes B';
      } else if (bytes < 1024 * 1024) {
        return '${(bytes / 1024).toStringAsFixed(1)} KB';
      } else {
        return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
      }
    } catch (e) {
      return 'Unknown size';
    }
  }
}
